<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Eat Hub - نظام إدارة المطعم للموظفين</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: '#FF6B35',      // برتقالي محمر
                        secondary: '#4CAF50',    // أخضر للدلالة على الطازج
                        accent: '#FFEB3B',       // أصفر للتنبيهات والعروض
                        warmBrown: '#C8A080',    // بني دافئ للراحة
                        darkText: '#333333',     // للنصوص الداكنة
                    },
                    fontFamily: {
                        sans: ['Cairo', 'sans-serif'],
                    },
                }
            }
        };

        // تحميل الثيم المحفوظ من localStorage
        const savedTheme = localStorage.getItem('theme_preference') || 'auto';
        const effectiveTheme = localStorage.getItem('effective_theme') || 'light';

        // تطبيق الثيم فوراً لتجنب الوميض
        if (savedTheme === 'dark' || (savedTheme === 'auto' && effectiveTheme === 'dark')) {
            document.documentElement.classList.add('dark');
        }
    </script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.css" rel="stylesheet">
    <link href="{{ asset('css/theme.css') }}" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;600;700&display=swap');

        body {
            font-family: 'Cairo', sans-serif;
        }

        /* تنسيق السكرولبار */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .dark ::-webkit-scrollbar-track {
            background: #2d3748;
        }

        ::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #FF6B35;
        }

        /* تحريك القائمة الجانبية بتأثير سلس */
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        /* تأثيرات الحركة */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* تنسيق البطاقات */
        .card-hover:hover {
            transform: translateY(-5px);
            transition: transform 0.3s ease;
        }

        /* تنسيق زر تحديث الحالة */
        .status-btn {
            transition: all 0.3s ease;
        }

        .status-btn:hover {
            transform: translateY(-2px);
        }

        /* خاص بلوحة الموظف - الطاولات */
        .table-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }

        .restaurant-table {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .restaurant-table:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .table-available {
            background-color: rgba(74, 222, 128, 0.2);
            border: 2px solid #4ade80;
        }

        .table-occupied {
            background-color: rgba(248, 113, 113, 0.2);
            border: 2px solid #f87171;
        }

        .table-reserved {
            background-color: rgba(251, 191, 36, 0.2);
            border: 2px solid #fbbf24;
        }

        .table-number {
            font-size: 1.5rem;
            font-weight: 700;
        }

        .table-status {
            font-size: 0.875rem;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            margin-top: 0.5rem;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="flex h-screen overflow-hidden">
        <!-- استدعاء القائمة الجانبية -->
        @include('employee.layouts.sidebar')

        <!-- المحتوى الرئيسي -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- استدعاء الهيدر -->
            @include('employee.layouts.header')

            <!-- المحتوى الرئيسي -->
            <main class="flex-1 overflow-y-auto p-4 bg-gray-100 dark:bg-gray-900">
                @yield('content')
            </main>
        </div>
    </div>

    <!-- استدعاء ملف السكريبتات -->
    @include('employee.layouts.scripts')
</body>
</html>