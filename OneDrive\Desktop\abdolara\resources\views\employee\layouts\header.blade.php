<!-- الهيدر -->
<header class="bg-white dark:bg-gray-800 shadow-md z-10">
    <div class="px-4 py-3 flex justify-between items-center">
        <!-- زر فتح/إغلاق القائمة الجانبية -->
        <button id="sidebarToggle" class="md:hidden p-2 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
            <i class="fas fa-bars text-xl"></i>
        </button>

        <!-- عنوان الصفحة - سيتم تغييره ديناميكياً -->
        <h1 id="pageTitle" class="text-xl font-bold hidden md:block text-gray-800 dark:text-white">لوحة التحكم</h1>

        <div class="flex items-center space-x-4 space-x-reverse">
            <!-- زر البحث -->
            <div class="relative">
                <form action="{{ route('search') }}" method="GET" class="hidden md:flex items-center">
                    <input type="text" name="query" placeholder="بحث..." class="w-48 px-3 py-1 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary text-sm">
                    <button type="submit" class="absolute left-2 top-1.5 text-gray-500 dark:text-gray-400">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                <button id="mobileSearchBtn" class="md:hidden p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                    <i class="fas fa-search"></i>
                </button>
            </div>

            <!-- زر سلة الطلبات -->
            <div class="relative">
                <a href="#" id="cartButton" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 inline-block">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center">0</span>
                </a>
                <!-- قائمة سلة الطلبات - ستظهر عند النقر -->
                <div id="cartMenu" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">سلة الطلبات</h3>
                        <a href="{{ route('employee.orders.create') }}" class="text-primary text-sm hover:underline">إنشاء طلب</a>
                    </div>
                    <div id="cartItemsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بعناصر السلة من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>لا توجد عناصر في السلة</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر الإشعارات -->
            <div class="relative">
                <a href="{{ route('employee.notifications') }}" class="p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 inline-block">
                    <i class="fas fa-bell"></i>
                    <span id="notificationCount" class="absolute -top-1 -right-1 bg-primary text-white rounded-full text-xs w-5 h-5 flex items-center justify-center">0</span>
                </a>
                <!-- قائمة الإشعارات - ستظهر عند النقر -->
                <div id="notificationsMenu" class="absolute left-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden">
                    <div class="px-4 py-2 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="font-bold text-gray-800 dark:text-white">الإشعارات</h3>
                        <a href="{{ route('employee.notifications') }}" class="text-primary text-sm hover:underline">عرض الكل</a>
                    </div>
                    <div id="notificationsList" class="max-h-64 overflow-y-auto">
                        <!-- ستتم تعبئة هذا القسم بالإشعارات من خلال JavaScript -->
                        <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                            <p>جاري تحميل الإشعارات...</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- زر تبديل الثيم -->
            <button data-theme-toggle class="theme-toggle p-2 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all duration-300" title="تبديل الثيم">
                <i class="theme-icon fas fa-adjust"></i>
            </button>

            <!-- صورة المستخدم -->
            <div class="relative">
                <button id="userMenuBtn" class="flex items-center p-1 rounded-full text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700">
                    <div class="w-8 h-8 rounded-full bg-primary flex items-center justify-center text-white font-bold overflow-hidden">
                        @if(Auth::user()->profile_image)
                            <img src="{{ asset('storage/' . Auth::user()->profile_image) }}" alt="{{ Auth::user()->first_name }}" class="w-full h-full object-cover">
                        @else
                            {{ substr(Auth::user()->first_name, 0, 1) }}
                        @endif
                    </div>
                    <span class="mr-2 hidden md:block">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</span>
                    <i class="fas fa-chevron-down text-xs mr-1 hidden md:block"></i>
                </button>

                <!-- قائمة المستخدم - مخفية افتراضياً -->
                <div id="userMenu" class="absolute left-0 mt-2 w-64 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 hidden">
                    <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                        <p class="text-sm font-medium text-gray-900 dark:text-white">{{ Auth::user()->first_name }} {{ Auth::user()->last_name }}</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400 truncate">{{ Auth::user()->email }}</p>
                    </div>
                    <a href="{{ route('employee.profile') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-user-circle ml-2"></i>الملف الشخصي
                    </a>

                    @if(Auth::user()->user_type == 'admin' || Auth::user()->can('dashboard.admin'))
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <div class="px-3 py-1">
                        <span class="text-xs text-gray-500 dark:text-gray-400 font-medium">التنقل السريع</span>
                    </div>
                    <a href="{{ route('admin.dashboard') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 dark:hover:text-green-400 transition-colors">
                        <i class="fas fa-user-shield ml-2 text-green-500"></i>واجهة المدير
                        <span class="text-xs text-gray-500 dark:text-gray-400 block">العودة لوحة الإدارة</span>
                    </a>
                    @endif
                    <a href="{{ route('employee.settings') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-cog ml-2"></i>الإعدادات
                    </a>
                    <a href="{{ route('employee.help') }}" class="block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                        <i class="fas fa-question-circle ml-2"></i>المساعدة
                    </a>
                    <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                    <form action="{{ route('logout') }}" method="POST" class="w-full">
                        @csrf
                        <button type="submit" class="w-full text-right block px-4 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <i class="fas fa-sign-out-alt ml-2"></i>تسجيل الخروج
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</header>