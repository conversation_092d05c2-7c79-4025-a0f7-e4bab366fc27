<!-- سكريبت الصفحة -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.js"></script>
<script src="{{ asset('js/theme-manager.js') }}"></script>
<script>

    // إدارة القائمة الجانبية للموبايل
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');

    sidebarToggle.addEventListener('click', function() {
        mobileMenu.classList.remove('-translate-x-full');
    });

    closeMobileMenu.addEventListener('click', function() {
        mobileMenu.classList.add('-translate-x-full');
    });

    // إدارة قائمة المستخدم
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    userMenuBtn.addEventListener('click', function() {
        userMenu.classList.toggle('hidden');
    });

    // إغلاق قائمة المستخدم عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
            userMenu.classList.add('hidden');
        }
    });

    // إدارة سلة الطلبات
    const cartButton = document.getElementById('cartButton');
    const cartMenu = document.getElementById('cartMenu');

    if (cartButton && cartMenu) {
        // فتح/إغلاق قائمة سلة الطلبات عند النقر على الزر
        cartButton.addEventListener('click', function(e) {
            e.preventDefault();
            cartMenu.classList.toggle('hidden');

            // تحديث محتوى السلة عند فتحها
            if (!cartMenu.classList.contains('hidden')) {
                loadCartItems();
            }
        });

        // إغلاق قائمة سلة الطلبات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!cartButton.contains(event.target) && !cartMenu.contains(event.target)) {
                cartMenu.classList.add('hidden');
            }
        });
    }

    // دالة تحميل عناصر السلة
    function loadCartItems() {
        const cartItemsList = document.getElementById('cartItemsList');

        // استرجاع عناصر السلة من التخزين المحلي
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        if (cartItems.length === 0) {
            cartItemsList.innerHTML = `
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    <p>لا توجد عناصر في السلة</p>
                </div>
            `;
            return;
        }

        // عرض عناصر السلة
        let cartHtml = '';
        let totalAmount = 0;

        cartItems.forEach(item => {
            const itemTotal = item.price * item.quantity;
            totalAmount += itemTotal;

            cartHtml += `
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-white">${item.name}</h4>
                            <div class="flex items-center mt-1">
                                <span class="text-sm text-gray-500 dark:text-gray-400">الكمية: ${item.quantity}</span>
                                <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">${item.price.toFixed(2)} د.ل</span>
                            </div>
                        </div>
                        <button class="text-red-500 hover:text-red-700" onclick="removeCartItem('${item.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        // إضافة المجموع وزر إنشاء طلب
        cartHtml += `
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <span class="font-bold text-gray-800 dark:text-white">المجموع:</span>
                    <span class="font-bold text-primary">${totalAmount.toFixed(2)} د.ل</span>
                </div>
            </div>
            <div class="px-4 py-3">
                <a href="{{ route('employee.orders.create') }}" class="block w-full bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-md">
                    إنشاء طلب
                </a>
            </div>
        `;

        cartItemsList.innerHTML = cartHtml;
    }

    // دالة إزالة عنصر من السلة
    window.removeCartItem = function(itemId) {
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        cartItems = cartItems.filter(item => item.id !== itemId);
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر في السلة
        updateCartCount();

        // تحديث محتوى السلة
        loadCartItems();
    }

    // دالة تحديث عدد العناصر في السلة
    function updateCartCount() {
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        const cartCountElements = document.querySelectorAll('.cart-count');

        cartCountElements.forEach(element => {
            element.textContent = cartItems.length;
        });
    }

    // تحديث عدد العناصر في السلة عند تحميل الصفحة
    updateCartCount();

    // إدارة الإشعارات
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationsMenu = document.getElementById('notificationsMenu');
    const notificationsList = document.getElementById('notificationsList');
    const notificationCount = document.getElementById('notificationCount');

    if (notificationBtn && notificationsMenu) {
        // فتح/إغلاق قائمة الإشعارات
        notificationBtn.addEventListener('click', function(e) {
            e.preventDefault();
            notificationsMenu.classList.toggle('hidden');

            // تحميل الإشعارات عند فتح القائمة
            if (!notificationsMenu.classList.contains('hidden')) {
                loadNotifications();
            }
        });

        // إغلاق قائمة الإشعارات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!notificationBtn.contains(event.target) && !notificationsMenu.contains(event.target)) {
                notificationsMenu.classList.add('hidden');
            }
        });
    }

    // دالة تحميل الإشعارات
    function loadNotifications() {
        if (!notificationsList) return;

        notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>جاري تحميل الإشعارات...</p></div>';

        fetch('/api/notifications/latest')
            .then(response => response.json())
            .then(data => {
                displayNotifications(data.notifications || []);
            })
            .catch(error => {
                console.error('Error loading notifications:', error);
                notificationsList.innerHTML = '<div class="text-center py-4 text-red-500"><p>حدث خطأ في تحميل الإشعارات</p></div>';
            });
    }

    // دالة عرض الإشعارات
    function displayNotifications(notifications) {
        if (!notificationsList) return;

        if (notifications.length === 0) {
            notificationsList.innerHTML = '<div class="text-center py-4 text-gray-500 dark:text-gray-400"><p>لا توجد إشعارات جديدة</p></div>';
            return;
        }

        let html = '';
        notifications.forEach(notification => {
            const icon = getNotificationIcon(notification.type);
            const timeAgo = getTimeAgo(notification.created_at);

            html += `
                <div class="px-4 py-3 hover:bg-gray-50 dark:hover:bg-gray-700/30 border-b border-gray-100 dark:border-gray-700 last:border-b-0 cursor-pointer notification-item ${notification.is_read ? 'read' : 'unread'}"
                     onclick="handleNotificationClick(${notification.notification_id}, '${notification.action_url || ''}')">
                    <div class="flex items-start">
                        <div class="flex-shrink-0 ml-3">
                            <div class="h-8 w-8 rounded-full ${icon.bgColor} flex items-center justify-center ${icon.textColor}">
                                <i class="${icon.class}"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <div class="flex items-center justify-between">
                                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                                    ${notification.title || 'إشعار جديد'}
                                </p>
                                ${!notification.is_read ? '<div class="w-2 h-2 bg-primary rounded-full"></div>' : ''}
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                                ${notification.message}
                            </p>
                            <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                                ${timeAgo}
                            </p>
                        </div>
                    </div>
                </div>
            `;
        });

        notificationsList.innerHTML = html;
    }

    // دالة الحصول على أيقونة الإشعار
    function getNotificationIcon(type) {
        const icons = {
            'order': {
                class: 'fas fa-shopping-cart',
                bgColor: 'bg-blue-100 dark:bg-blue-900/30',
                textColor: 'text-blue-500 dark:text-blue-300'
            },
            'reservation': {
                class: 'fas fa-calendar-check',
                bgColor: 'bg-green-100 dark:bg-green-900/30',
                textColor: 'text-green-500 dark:text-green-300'
            },
            'inventory': {
                class: 'fas fa-box',
                bgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
                textColor: 'text-yellow-500 dark:text-yellow-300'
            },
            'system': {
                class: 'fas fa-cog',
                bgColor: 'bg-purple-100 dark:bg-purple-900/30',
                textColor: 'text-purple-500 dark:text-purple-300'
            },
            'default': {
                class: 'fas fa-bell',
                bgColor: 'bg-gray-100 dark:bg-gray-700',
                textColor: 'text-gray-500 dark:text-gray-300'
            }
        };

        return icons[type] || icons['default'];
    }

    // دالة حساب الوقت المنقضي
    function getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);

        if (diffInSeconds < 60) {
            return 'الآن';
        } else if (diffInSeconds < 3600) {
            const minutes = Math.floor(diffInSeconds / 60);
            return `منذ ${minutes} دقيقة`;
        } else if (diffInSeconds < 86400) {
            const hours = Math.floor(diffInSeconds / 3600);
            return `منذ ${hours} ساعة`;
        } else {
            const days = Math.floor(diffInSeconds / 86400);
            return `منذ ${days} يوم`;
        }
    }

    // دالة تحديث عدد الإشعارات
    function updateNotificationCount() {
        fetch('/api/notifications/count')
            .then(response => response.json())
            .then(data => {
                const count = data.count || 0;
                if (notificationCount) {
                    notificationCount.textContent = count;
                    if (count > 0) {
                        notificationCount.classList.remove('hidden');
                    } else {
                        notificationCount.classList.add('hidden');
                    }
                }
            })
            .catch(error => {
                console.error('Error updating notification count:', error);
            });
    }

    // تحديث عدد الإشعارات عند تحميل الصفحة
    updateNotificationCount();

    // تحديث عدد الإشعارات كل 30 ثانية
    setInterval(updateNotificationCount, 30000);

    // دالة تعليم الإشعار كمقروء
    window.markNotificationAsRead = function(notificationId) {
        fetch(`/employee/notifications/${notificationId}/mark-read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عدد الإشعارات
                updateNotificationCount();
            }
        })
        .catch(error => {
            console.error('Error marking notification as read:', error);
        });
    };

    // دالة تعليم جميع الإشعارات كمقروءة
    window.markAllNotificationsAsRead = function() {
        fetch('/employee/notifications/mark-all-read', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث عدد الإشعارات
                updateNotificationCount();
                // إعادة تحميل الإشعارات
                if (!notificationsMenu.classList.contains('hidden')) {
                    loadNotifications();
                }
            }
        })
        .catch(error => {
            console.error('Error marking all notifications as read:', error);
        });
    };

    // دالة التعامل مع النقر على الإشعار
    window.handleNotificationClick = function(notificationId, actionUrl) {
        // تعليم الإشعار كمقروء
        markNotificationAsRead(notificationId);

        // إغلاق قائمة الإشعارات
        if (notificationsMenu) {
            notificationsMenu.classList.add('hidden');
        }

        // الانتقال إلى الرابط إذا كان موجود
        if (actionUrl && actionUrl !== '' && actionUrl !== 'null') {
            window.location.href = actionUrl;
        } else {
            // الانتقال إلى صفحة الإشعارات
            window.location.href = '/employee/notifications';
        }
    };

    // التنقل بين الصفحات
    const pageTitle = document.getElementById('pageTitle');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    // تحديث حالة الروابط النشطة
    function updateActiveLinks() {
        const currentPath = window.location.pathname;
        const pageId = currentPath.split('/').pop() || 'dashboard';

        // تحديث العنوان
        pageTitle.textContent = pageId === 'dashboard' ? 'لوحة التحكم' :
                               pageId === 'orders' ? 'إدارة الطلبات' :
                               pageId === 'reservations' ? 'إدارة الحجوزات' :
                               pageId === 'tables' ? 'حالة الطاولات' :
                               pageId === 'payments' ? 'المدفوعات' :
                               pageId === 'menu' ? 'قائمة الطعام' :
                               pageId === 'notifications' ? 'الإشعارات' : 'لوحة التحكم';

        // تحديث حالة الروابط
        navLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });

        mobileNavLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });
    }

    // تنفيذ عند تحميل الصفحة
    updateActiveLinks();

    // ضمان ثبات الثيم - حل نهائي
    function forceThemeConsistency() {
        const savedTheme = localStorage.getItem('theme_preference') || 'light';
        let themeToApply = savedTheme;

        if (savedTheme === 'auto') {
            themeToApply = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }

        const html = document.documentElement;

        // تطبيق الثيم بقوة
        if (!html.classList.contains(themeToApply)) {
            html.className = ''; // مسح جميع الكلاسات
            html.classList.add(themeToApply);
            html.setAttribute('data-theme', themeToApply);

            // حفظ الثيم
            localStorage.setItem('theme_preference', savedTheme);
            localStorage.setItem('effective_theme', themeToApply);
        }
    }

    // تطبيق فوري
    forceThemeConsistency();

    // مراقبة مستمرة كل ثانية
    setInterval(forceThemeConsistency, 1000);

    // مراقبة عند تغيير الصفحة
    window.addEventListener('beforeunload', forceThemeConsistency);
    window.addEventListener('pageshow', forceThemeConsistency);

    // ربط أحداث النقر بالروابط مع ضمان ثبات الثيم
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
        });
    });

    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
            // إغلاق القائمة الجانبية للموبايل
            mobileMenu.classList.add('-translate-x-full');
        });
    });

    // نموذج إنشاء طلب جديد
    const openNewOrderBtns = document.querySelectorAll('button.bg-primary:has(.fas.fa-plus)');
    const newOrderModal = document.getElementById('new-order-modal');
    const closeNewOrderBtns = document.querySelectorAll('#close-new-order, #cancel-new-order');

    if (openNewOrderBtns.length > 0 && newOrderModal) {
        openNewOrderBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (newOrderModal) {
                    newOrderModal.classList.remove('hidden');
                }
            });
        });

        if (closeNewOrderBtns.length > 0) {
            closeNewOrderBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    newOrderModal.classList.add('hidden');
                });
            });
        }

        // إغلاق النموذج عند النقر خارجه
        newOrderModal.addEventListener('click', function(e) {
            if (e.target === newOrderModal) {
                newOrderModal.classList.add('hidden');
            }
        });
    }

    // مخطط المبيعات
    if (document.getElementById('salesChart')) {
        const salesChartOptions = {
            series: [{
                name: 'المبيعات',
                data: [1800, 1600, 2200, 1800, 2400, 1900, 2100]
            }],
            chart: {
                height: 288,
                type: 'area',
                fontFamily: 'Cairo, sans-serif',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            colors: ['#FF6B35'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                    stops: [0, 90, 100]
                }
            },
            grid: {
                borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
            },
            xaxis: {
                categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    },
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    }
                }
            }
        };

        const salesChart = new ApexCharts(document.getElementById('salesChart'), salesChartOptions);
        salesChart.render();
    }

    // تم إزالة تعيين الصفحة الافتراضية لأننا نستخدم الراوت الآن
</script>