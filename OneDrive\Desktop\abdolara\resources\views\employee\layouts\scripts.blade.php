<!-- سكريبت الصفحة -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.3/dist/apexcharts.min.js"></script>
<script src="{{ asset('js/theme-manager.js') }}"></script>
<script>

    // إدارة القائمة الجانبية للموبايل
    const sidebarToggle = document.getElementById('sidebarToggle');
    const mobileMenu = document.getElementById('mobileMenu');
    const closeMobileMenu = document.getElementById('closeMobileMenu');

    sidebarToggle.addEventListener('click', function() {
        mobileMenu.classList.remove('-translate-x-full');
    });

    closeMobileMenu.addEventListener('click', function() {
        mobileMenu.classList.add('-translate-x-full');
    });

    // إدارة قائمة المستخدم
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userMenu = document.getElementById('userMenu');

    userMenuBtn.addEventListener('click', function() {
        userMenu.classList.toggle('hidden');
    });

    // إغلاق قائمة المستخدم عند النقر خارجها
    document.addEventListener('click', function(event) {
        if (!userMenuBtn.contains(event.target) && !userMenu.contains(event.target)) {
            userMenu.classList.add('hidden');
        }
    });

    // إدارة سلة الطلبات
    const cartButton = document.getElementById('cartButton');
    const cartMenu = document.getElementById('cartMenu');

    if (cartButton && cartMenu) {
        // فتح/إغلاق قائمة سلة الطلبات عند النقر على الزر
        cartButton.addEventListener('click', function(e) {
            e.preventDefault();
            cartMenu.classList.toggle('hidden');

            // تحديث محتوى السلة عند فتحها
            if (!cartMenu.classList.contains('hidden')) {
                loadCartItems();
            }
        });

        // إغلاق قائمة سلة الطلبات عند النقر خارجها
        document.addEventListener('click', function(event) {
            if (!cartButton.contains(event.target) && !cartMenu.contains(event.target)) {
                cartMenu.classList.add('hidden');
            }
        });
    }

    // دالة تحميل عناصر السلة
    function loadCartItems() {
        const cartItemsList = document.getElementById('cartItemsList');

        // استرجاع عناصر السلة من التخزين المحلي
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];

        if (cartItems.length === 0) {
            cartItemsList.innerHTML = `
                <div class="text-center py-4 text-gray-500 dark:text-gray-400">
                    <p>لا توجد عناصر في السلة</p>
                </div>
            `;
            return;
        }

        // عرض عناصر السلة
        let cartHtml = '';
        let totalAmount = 0;

        cartItems.forEach(item => {
            const itemTotal = item.price * item.quantity;
            totalAmount += itemTotal;

            cartHtml += `
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex justify-between items-start">
                        <div>
                            <h4 class="font-medium text-gray-800 dark:text-white">${item.name}</h4>
                            <div class="flex items-center mt-1">
                                <span class="text-sm text-gray-500 dark:text-gray-400">الكمية: ${item.quantity}</span>
                                <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">${item.price.toFixed(2)} د.ل</span>
                            </div>
                        </div>
                        <button class="text-red-500 hover:text-red-700" onclick="removeCartItem('${item.id}')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        // إضافة المجموع وزر إنشاء طلب
        cartHtml += `
            <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <span class="font-bold text-gray-800 dark:text-white">المجموع:</span>
                    <span class="font-bold text-primary">${totalAmount.toFixed(2)} د.ل</span>
                </div>
            </div>
            <div class="px-4 py-3">
                <a href="{{ route('employee.orders.create') }}" class="block w-full bg-primary hover:bg-primary/90 text-white text-center py-2 px-4 rounded-md">
                    إنشاء طلب
                </a>
            </div>
        `;

        cartItemsList.innerHTML = cartHtml;
    }

    // دالة إزالة عنصر من السلة
    window.removeCartItem = function(itemId) {
        let cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        cartItems = cartItems.filter(item => item.id !== itemId);
        localStorage.setItem('cartItems', JSON.stringify(cartItems));

        // تحديث عدد العناصر في السلة
        updateCartCount();

        // تحديث محتوى السلة
        loadCartItems();
    }

    // دالة تحديث عدد العناصر في السلة
    function updateCartCount() {
        const cartItems = JSON.parse(localStorage.getItem('cartItems')) || [];
        const cartCountElements = document.querySelectorAll('.cart-count');

        cartCountElements.forEach(element => {
            element.textContent = cartItems.length;
        });
    }

    // تحديث عدد العناصر في السلة عند تحميل الصفحة
    updateCartCount();

    // التنقل بين الصفحات
    const pageTitle = document.getElementById('pageTitle');
    const navLinks = document.querySelectorAll('.nav-link');
    const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

    // تحديث حالة الروابط النشطة
    function updateActiveLinks() {
        const currentPath = window.location.pathname;
        const pageId = currentPath.split('/').pop() || 'dashboard';

        // تحديث العنوان
        pageTitle.textContent = pageId === 'dashboard' ? 'لوحة التحكم' :
                               pageId === 'orders' ? 'إدارة الطلبات' :
                               pageId === 'reservations' ? 'إدارة الحجوزات' :
                               pageId === 'tables' ? 'حالة الطاولات' :
                               pageId === 'payments' ? 'المدفوعات' :
                               pageId === 'menu' ? 'قائمة الطعام' :
                               pageId === 'notifications' ? 'الإشعارات' : 'لوحة التحكم';

        // تحديث حالة الروابط
        navLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });

        mobileNavLinks.forEach(link => {
            if (link.dataset.page === pageId) {
                link.classList.add('bg-primary/10', 'text-primary');
            } else {
                link.classList.remove('bg-primary/10', 'text-primary');
            }
        });
    }

    // تنفيذ عند تحميل الصفحة
    updateActiveLinks();

    // ضمان ثبات الثيم عند التنقل
    function ensureThemeConsistency() {
        const savedTheme = localStorage.getItem('theme_preference');
        const effectiveTheme = localStorage.getItem('effective_theme');

        if (savedTheme && effectiveTheme) {
            const html = document.documentElement;
            html.classList.remove('light', 'dark');
            html.classList.add(effectiveTheme);
        }
    }

    // تطبيق ضمان الثبات
    ensureThemeConsistency();

    // ربط أحداث النقر بالروابط مع ضمان ثبات الثيم
    navLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
        });
    });

    mobileNavLinks.forEach(link => {
        link.addEventListener('click', function() {
            const pageId = this.dataset.page;
            // حفظ الثيم قبل التنقل
            const currentTheme = localStorage.getItem('theme_preference');
            const currentEffectiveTheme = localStorage.getItem('effective_theme');
            if (currentTheme) {
                localStorage.setItem('theme_preference', currentTheme);
                localStorage.setItem('effective_theme', currentEffectiveTheme);
            }
            window.location.href = `/employee/${pageId === 'dashboard' ? '' : pageId}`;
            // إغلاق القائمة الجانبية للموبايل
            mobileMenu.classList.add('-translate-x-full');
        });
    });

    // نموذج إنشاء طلب جديد
    const openNewOrderBtns = document.querySelectorAll('button.bg-primary:has(.fas.fa-plus)');
    const newOrderModal = document.getElementById('new-order-modal');
    const closeNewOrderBtns = document.querySelectorAll('#close-new-order, #cancel-new-order');

    if (openNewOrderBtns.length > 0 && newOrderModal) {
        openNewOrderBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (newOrderModal) {
                    newOrderModal.classList.remove('hidden');
                }
            });
        });

        if (closeNewOrderBtns.length > 0) {
            closeNewOrderBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    newOrderModal.classList.add('hidden');
                });
            });
        }

        // إغلاق النموذج عند النقر خارجه
        newOrderModal.addEventListener('click', function(e) {
            if (e.target === newOrderModal) {
                newOrderModal.classList.add('hidden');
            }
        });
    }

    // مخطط المبيعات
    if (document.getElementById('salesChart')) {
        const salesChartOptions = {
            series: [{
                name: 'المبيعات',
                data: [1800, 1600, 2200, 1800, 2400, 1900, 2100]
            }],
            chart: {
                height: 288,
                type: 'area',
                fontFamily: 'Cairo, sans-serif',
                toolbar: {
                    show: false
                },
                zoom: {
                    enabled: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            colors: ['#FF6B35'],
            fill: {
                type: 'gradient',
                gradient: {
                    shadeIntensity: 1,
                    opacityFrom: 0.7,
                    opacityTo: 0.3,
                    stops: [0, 90, 100]
                }
            },
            grid: {
                borderColor: document.documentElement.classList.contains('dark') ? '#374151' : '#e5e7eb',
            },
            xaxis: {
                categories: ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'],
                labels: {
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            yaxis: {
                labels: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    },
                    style: {
                        colors: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#6b7280',
                        fontFamily: 'Cairo, sans-serif'
                    }
                }
            },
            tooltip: {
                y: {
                    formatter: function(value) {
                        return value + ' ر.س';
                    }
                }
            }
        };

        const salesChart = new ApexCharts(document.getElementById('salesChart'), salesChartOptions);
        salesChart.render();
    }

    // تم إزالة تعيين الصفحة الافتراضية لأننا نستخدم الراوت الآن
</script>