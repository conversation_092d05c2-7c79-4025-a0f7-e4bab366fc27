/**
 * نظام الثيم - الوضع الليلي والنهاري
 */

/* المتغيرات الأساسية */
:root {
    /* الوضع النهاري (افتراضي) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* الألوان الأساسية */
    --primary-color: #3b82f6;
    --primary-hover: #2563eb;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;
    
    /* ألوان الحالة */
    --success-bg: #d1fae5;
    --success-text: #065f46;
    --warning-bg: #fef3c7;
    --warning-text: #92400e;
    --danger-bg: #fee2e2;
    --danger-text: #991b1b;
    --info-bg: #cffafe;
    --info-text: #155e75;
}

/* الوضع الليلي */
html.dark {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --bg-tertiary: #4a5568;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --text-muted: #a0aec0;
    --border-color: #4a5568;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    
    /* ألوان الحالة للوضع الليلي */
    --success-bg: #064e3b;
    --success-text: #6ee7b7;
    --warning-bg: #78350f;
    --warning-text: #fbbf24;
    --danger-bg: #7f1d1d;
    --danger-text: #fca5a5;
    --info-bg: #164e63;
    --info-text: #67e8f9;
}

/* تطبيق المتغيرات على العناصر */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* الخلفيات */
.bg-white { background-color: var(--bg-primary) !important; }
.bg-gray-50 { background-color: var(--bg-secondary) !important; }
.bg-gray-100 { background-color: var(--bg-tertiary) !important; }

/* النصوص */
.text-gray-900 { color: var(--text-primary) !important; }
.text-gray-700 { color: var(--text-secondary) !important; }
.text-gray-500 { color: var(--text-muted) !important; }

/* الحدود */
.border-gray-200 { border-color: var(--border-color) !important; }
.border-gray-300 { border-color: var(--border-color) !important; }

/* الظلال */
.shadow { box-shadow: var(--shadow) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* البطاقات والحاويات */
.card,
.bg-white,
.bg-gray-50 {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

/* الجداول */
table {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

table th {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

table td {
    border-color: var(--border-color);
    color: var(--text-secondary);
}

table tr:hover {
    background-color: var(--bg-secondary);
}

/* النماذج */
input,
textarea,
select {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

input:focus,
textarea:focus,
select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* الأزرار */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* الإشعارات */
.alert-success {
    background-color: var(--success-bg);
    color: var(--success-text);
    border-color: var(--success-color);
}

.alert-warning {
    background-color: var(--warning-bg);
    color: var(--warning-text);
    border-color: var(--warning-color);
}

.alert-danger {
    background-color: var(--danger-bg);
    color: var(--danger-text);
    border-color: var(--danger-color);
}

.alert-info {
    background-color: var(--info-bg);
    color: var(--info-text);
    border-color: var(--info-color);
}

/* الشريط الجانبي */
.sidebar {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
}

.sidebar .nav-link {
    color: var(--text-secondary);
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* الشريط العلوي */
.navbar {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    box-shadow: var(--shadow-lg);
}

.dropdown-item {
    color: var(--text-secondary);
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* أزرار الثيم */
.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

/* الرسوم البيانية والإحصائيات */
.chart-container {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

/* التحميل والسبينر */
.spinner {
    border-color: var(--border-color);
    border-top-color: var(--primary-color);
}

/* الانتقالات السلسة */
* {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* تخصيصات إضافية للوضع الليلي */
html.dark img {
    opacity: 0.9;
}

html.dark .text-black {
    color: var(--text-primary) !important;
}

html.dark .bg-blue-500 {
    background-color: #3b82f6 !important;
}

html.dark .bg-green-500 {
    background-color: #10b981 !important;
}

html.dark .bg-red-500 {
    background-color: #ef4444 !important;
}

html.dark .bg-yellow-500 {
    background-color: #f59e0b !important;
}

/* تحسينات للقراءة */
html.dark code {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

html.dark pre {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

/* تخصيصات للمكونات المخصصة */
.custom-card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.custom-header {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}
