/**
 * مدير الثيم - إدارة الوضع الليلي/النهاري
 */
class ThemeManager {
    constructor() {
        this.currentTheme = 'auto';
        this.init();
    }

    init() {
        // تحميل الثيم المحفوظ
        this.loadSavedTheme();
        
        // تطبيق الثيم
        this.applyTheme();
        
        // إضافة مستمعي الأحداث
        this.attachEventListeners();
        
        // مراقبة تغييرات النظام
        this.watchSystemTheme();
    }

    /**
     * تحميل الثيم المحفوظ
     */
    async loadSavedTheme() {
        try {
            // محاولة تحميل من الخادم للمستخدمين المسجلين
            const response = await fetch('/theme/current');
            if (response.ok) {
                const data = await response.json();
                this.currentTheme = data.theme || 'auto';
            }
        } catch (error) {
            // في حالة الفشل، استخدم localStorage
            this.currentTheme = localStorage.getItem('theme_preference') || 'auto';
        }
    }

    /**
     * تطبيق الثيم
     */
    applyTheme() {
        const html = document.documentElement;
        
        // إزالة جميع فئات الثيم
        html.classList.remove('light', 'dark');
        
        let effectiveTheme = this.currentTheme;
        
        // إذا كان الثيم تلقائي، استخدم إعدادات النظام
        if (this.currentTheme === 'auto') {
            effectiveTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        // تطبيق الثيم
        html.classList.add(effectiveTheme);
        
        // تحديث أيقونة الثيم
        this.updateThemeIcon(effectiveTheme);
        
        // حفظ في localStorage
        localStorage.setItem('theme_preference', this.currentTheme);
        localStorage.setItem('effective_theme', effectiveTheme);
    }

    /**
     * تحديث أيقونة الثيم
     */
    updateThemeIcon(effectiveTheme) {
        const themeButtons = document.querySelectorAll('[data-theme-toggle]');
        const themeIcons = document.querySelectorAll('.theme-icon');
        
        themeButtons.forEach(button => {
            const icon = button.querySelector('i') || button.querySelector('.theme-icon');
            if (icon) {
                // إزالة جميع فئات الأيقونات
                icon.classList.remove('fa-sun', 'fa-moon', 'fa-adjust');
                
                // إضافة الأيقونة المناسبة
                switch (this.currentTheme) {
                    case 'light':
                        icon.classList.add('fa-sun');
                        break;
                    case 'dark':
                        icon.classList.add('fa-moon');
                        break;
                    case 'auto':
                    default:
                        icon.classList.add('fa-adjust');
                        break;
                }
            }
        });
    }

    /**
     * تبديل الثيم
     */
    async toggleTheme() {
        const themes = ['auto', 'light', 'dark'];
        const currentIndex = themes.indexOf(this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        
        await this.setTheme(themes[nextIndex]);
    }

    /**
     * تعيين ثيم محدد
     */
    async setTheme(theme) {
        if (!['light', 'dark', 'auto'].includes(theme)) {
            theme = 'auto';
        }
        
        this.currentTheme = theme;
        this.applyTheme();
        
        // حفظ على الخادم
        try {
            await fetch('/theme/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({ theme: theme })
            });
        } catch (error) {
            console.warn('فشل في حفظ الثيم على الخادم:', error);
        }
        
        // إظهار رسالة تأكيد
        this.showThemeChangeNotification(theme);
    }

    /**
     * إضافة مستمعي الأحداث
     */
    attachEventListeners() {
        // أزرار تبديل الثيم
        document.addEventListener('click', (e) => {
            const themeToggle = e.target.closest('[data-theme-toggle]');
            if (themeToggle) {
                e.preventDefault();
                this.toggleTheme();
            }
        });

        // اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + Shift + T لتبديل الثيم
            if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.toggleTheme();
            }
        });
    }

    /**
     * مراقبة تغييرات ثيم النظام
     */
    watchSystemTheme() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        
        mediaQuery.addEventListener('change', () => {
            if (this.currentTheme === 'auto') {
                this.applyTheme();
            }
        });
    }

    /**
     * إظهار رسالة تأكيد تغيير الثيم
     */
    showThemeChangeNotification(theme) {
        const messages = {
            'light': 'تم التبديل للوضع النهاري ☀️',
            'dark': 'تم التبديل للوضع الليلي 🌙',
            'auto': 'تم التبديل للوضع التلقائي ⚙️'
        };

        const message = messages[theme] || 'تم تغيير الثيم';
        
        // إنشاء إشعار مؤقت
        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full';
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // إظهار الإشعار
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // إخفاء الإشعار بعد 3 ثوان
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }

    /**
     * الحصول على الثيم الحالي
     */
    getCurrentTheme() {
        return this.currentTheme;
    }

    /**
     * الحصول على الثيم الفعال (بعد تطبيق القواعد التلقائية)
     */
    getEffectiveTheme() {
        if (this.currentTheme === 'auto') {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        return this.currentTheme;
    }
}

// تهيئة مدير الثيم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
});

// تصدير للاستخدام العام
window.ThemeManager = ThemeManager;
